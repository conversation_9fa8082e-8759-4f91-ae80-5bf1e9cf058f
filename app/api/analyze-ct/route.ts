import { NextRequest, NextResponse } from "next/server"
import { PatientStorage, ReportStorage, FileStorage } from "@/lib/storage"
import { ApiResponse, Patient } from "@/lib/types"

// 这是一个模拟的API路由。
// 在实际应用中，您需要处理文件上传（例如使用 formidable 或 busboy），
// 然后将文件发送到您的后端分析服务。
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const patientId = formData.get('patientId') as string
    const segmentationDataStr = formData.get('segmentationData') as string

    if (!file) {
      const response: ApiResponse<null> = {
        success: false,
        error: '请上传CT文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 解析分割和测量数据
    let segmentationData = null
    if (segmentationDataStr) {
      try {
        segmentationData = JSON.parse(segmentationDataStr)
        console.log('收到分割数据:', segmentationData)
      } catch (error) {
        console.warn('分割数据解析失败:', error)
      }
    }

    if (!file.name.endsWith('.nii.gz')) {
      const response: ApiResponse<null> = {
        success: false,
        error: '请上传 .nii.gz 格式的文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 获取或创建患者信息
    let patient: Patient | null = null
    if (patientId) {
      patient = await PatientStorage.getById(patientId)
      if (!patient) {
        const response: ApiResponse<null> = {
          success: false,
          error: '患者不存在'
        }
        return NextResponse.json(response, { status: 404 })
      }
    } else {
      // 如果没有提供患者ID，创建一个临时患者记录
      const patientName = formData.get('patientName') as string || '未知患者'
      const patientAge = parseInt(formData.get('patientAge') as string || '0')
      const patientGender = (formData.get('patientGender') as string || '男') as '男' | '女'
      
      patient = await PatientStorage.create({
        name: patientName,
        age: patientAge,
        gender: patientGender
      })
    }

    // 保存CT文件
    const filePath = await FileStorage.saveFile(file, 'ct-scans')

    // 模拟处理延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 基于分割和测量数据生成智能报告
    let keyFindings = []
    let recommendations = ""
    let riskFactors = []
    let difficulty: "低" | "中" | "高" = "中"
    let score = 75

    if (segmentationData && segmentationData.measurements) {
      // 基于测量数据生成发现
      const measurements = segmentationData.measurements
      const totalVolume = measurements.reduce((sum: number, m: any) => sum + m.measurements.volume.physicalVolumeML, 0)
      const structureCount = measurements.length

      keyFindings = [
        `检测到 ${structureCount} 个分割结构，总体积约 ${totalVolume.toFixed(2)} mL。`,
        ...measurements.map((m: any) =>
          `${m.labelName}: 体积 ${m.measurements.volume.physicalVolumeML.toFixed(2)} mL，表面积 ${m.measurements.surfaceArea.physicalAreaCM2.toFixed(2)} cm²`
        )
      ]

      // 基于体积评估难度
      if (totalVolume > 50) {
        difficulty = "高"
        score = 85
        riskFactors = ["病变体积较大", "多发病灶", "手术复杂度高"]
        recommendations = "建议多学科会诊，制定详细手术方案。考虑分期手术或术中冰冻病理检查。"
      } else if (totalVolume > 20) {
        difficulty = "中"
        score = 70
        riskFactors = ["病变体积中等", "需要精确定位", "功能区评估"]
        recommendations = "建议术前详细影像学评估，制定个体化手术方案。"
      } else {
        difficulty = "低"
        score = 60
        riskFactors = ["病变体积较小", "位置相对安全"]
        recommendations = "可考虑微创手术入路，术中注意保护周围正常组织。"
      }
    } else {
      // 默认模拟结果
      const difficulties: Array<"低" | "中" | "高"> = ["低", "中", "高"]
      difficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
      score = Math.floor(Math.random() * 100)

      keyFindings = [
        "左侧颞叶发现可疑病变区域。",
        "血管分布密集，与关键功能区接近。",
        "初步判断为高级别胶质瘤。"
      ]

      recommendations = "建议采用显微神经外科手术，并结合术中神经电生理监测，以最大程度保护神经功能。考虑术前进行功能磁共振成像（fMRI）以精确定位语言和运动中枢。"
      riskFactors = ["血管密集区域", "功能区邻近", "肿瘤体积较大"]
    }

    // 创建报告记录
    const report = await ReportStorage.create({
      patientId: patient.id,
      patientInfo: {
        id: patient.id,
        name: patient.name,
        age: patient.age,
        gender: patient.gender
      },
      analysisResults: {
        difficulty,
        score,
        keyFindings,
        recommendations,
        riskFactors,
        surgicalApproach: "显微神经外科手术",
        estimatedDuration: difficulty === "高" ? "6-8小时" : difficulty === "中" ? "4-6小时" : "2-4小时",
        postOpCare: "ICU监护24-48小时，神经功能监测"
      },
      summary: `患者${patient.name}，${patient.age}岁${patient.gender}性，CT影像分析和自动分割完成。${segmentationData ? `检测到${segmentationData.measurements?.length || 0}个结构，` : ''}综合评估手术难度为"${difficulty}"，风险评分为${score}。${recommendations}`,
      ctFileName: file.name,
      ctFilePath: filePath,
      status: "completed"
    })

    const response: ApiResponse<typeof report> = {
      success: true,
      data: report,
      message: 'CT分析完成'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('CT分析失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || 'CT分析过程中出现错误'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
