import { DicomCalibration, SegmentationMask, SegmentationModelConfig } from './types'

/**
 * 分割服务类 - 处理医学影像的自动分割
 * 注意：当前为开发测试阶段，实际分割模型调用未实现
 */
export class SegmentationService {
  private static modelConfigs: { [key: string]: SegmentationModelConfig } = {
    'tumor-segmentation-v1': {
      modelName: 'TumorSegNet',
      modelVersion: '1.0.0',
      inputSize: [512, 512, 128],
      outputClasses: {
        0: 'background',
        1: 'tumor_core',
        2: 'tumor_edema',
        3: 'necrosis'
      },
      preprocessing: {
        normalize: true,
        resample: true,
        targetSpacing: [1.0, 1.0, 1.0]
      },
      postprocessing: {
        fillHoles: true,
        smoothing: true,
        minComponentSize: 100,
        connectivityFilter: true
      }
    },
    'organ-segmentation-v1': {
      modelName: 'OrganSegNet',
      modelVersion: '1.0.0',
      inputSize: [512, 512, 256],
      outputClasses: {
        0: 'background',
        1: 'liver',
        2: 'kidney_left',
        3: 'kidney_right',
        4: 'spleen',
        5: 'pancreas'
      },
      preprocessing: {
        normalize: true,
        resample: false
      },
      postprocessing: {
        fillHoles: true,
        smoothing: false,
        minComponentSize: 50,
        connectivityFilter: true
      }
    }
  }

  /**
   * 执行自动分割（真实场景）
   * 注意：当前未实现，仅为接口定义
   */
  static async performSegmentation(
    imageData: ArrayBuffer,
    calibration: DicomCalibration,
    modelName: string = 'tumor-segmentation-v1'
  ): Promise<SegmentationMask> {
    // TODO: 实际实现中需要调用深度学习分割模型
    // 1. 预处理：标准化、重采样等
    // 2. 模型推理：调用训练好的分割模型
    // 3. 后处理：填充孔洞、平滑、连通域分析等
    
    console.log('🔬 [真实场景] 调用分割模型进行自动分割...')
    console.log(`模型: ${modelName}`)
    console.log(`输入数据大小: ${imageData.byteLength} bytes`)
    console.log(`像素间距: ${calibration.pixelSpacing}`)
    console.log(`层厚: ${calibration.sliceThickness}`)
    
    // 模拟分割过程
    await this.simulateSegmentationProcess()
    
    // 返回模拟的分割结果
    return this.createMockSegmentationMask(calibration, modelName)
  }

  /**
   * 从上传的标签文件加载分割掩码（开发测试阶段）
   */
  static async loadSegmentationFromLabel(
    labelFile: File,
    calibration: DicomCalibration
  ): Promise<SegmentationMask> {
    console.log('📁 [开发测试] 从标签文件加载分割掩码...')
    console.log(`文件: ${labelFile.name}`)
    
    try {
      const arrayBuffer = await labelFile.arrayBuffer()
      
      // 解析NIFTI标签文件
      const maskData = await this.parseNiftiLabel(arrayBuffer)
      
      return {
        id: `seg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        data: maskData.data,
        dimensions: maskData.dimensions,
        labels: maskData.labels,
        calibration,
        createdAt: new Date().toISOString(),
        modelVersion: 'manual-label',
        confidence: 1.0
      }
    } catch (error) {
      console.error('解析标签文件失败:', error)
      throw new Error(`标签文件解析失败: ${error.message}`)
    }
  }

  /**
   * 解析NIFTI标签文件
   */
  private static async parseNiftiLabel(arrayBuffer: ArrayBuffer): Promise<{
    data: Uint8Array | Uint16Array
    dimensions: [number, number, number]
    labels: { [key: number]: string }
  }> {
    // TODO: 实现完整的NIFTI解析
    // 当前为简化实现，实际需要使用专业的NIFTI库

    console.log(`🔍 解析NIFTI标签文件，原始大小: ${arrayBuffer.byteLength} bytes`)

    // 检查是否为gzip压缩
    const firstBytes = new Uint8Array(arrayBuffer.slice(0, 2))
    let decompressedBuffer = arrayBuffer

    if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
      console.log('🗜️ 检测到gzip压缩，正在解压...')
      try {
        decompressedBuffer = await this.decompressGzip(arrayBuffer)
        console.log(`✅ gzip解压成功，解压后大小: ${decompressedBuffer.byteLength} bytes`)
      } catch (error) {
        console.warn('❌ gzip解压失败，尝试直接解析:', error)
        // 如果解压失败，尝试跳过gzip头部
        decompressedBuffer = this.skipGzipHeader(arrayBuffer)
        console.log(`⚠️ 跳过gzip头部后大小: ${decompressedBuffer.byteLength} bytes`)
      }
    } else {
      console.log('📄 未检测到gzip压缩，直接解析')
    }

    // 简化的NIFTI头部解析
    const header = this.parseNiftiHeader(decompressedBuffer)
    console.log(`📋 NIFTI头部解析完成:`, header)

    // 提取图像数据
    const imageDataSize = decompressedBuffer.byteLength - header.voxOffset
    console.log(`🖼️ 图像数据大小: ${imageDataSize} bytes，偏移: ${header.voxOffset}`)

    const rawImageData = decompressedBuffer.slice(header.voxOffset)
    console.log(`🖼️ 提取的原始图像数据长度: ${rawImageData.byteLength} bytes`)

    // 根据数据类型创建正确的TypedArray
    const imageData = this.createTypedArrayFromBuffer(rawImageData, header.datatypeCode)
    console.log(`🖼️ 创建的TypedArray长度: ${imageData.length}，类型: ${imageData.constructor.name}`)

    // 计算期望的数据大小
    const expectedVoxelCount = header.dims[1] * header.dims[2] * header.dims[3]
    console.log(`📐 期望的体素数量: ${expectedVoxelCount} (${header.dims[1]} × ${header.dims[2]} × ${header.dims[3]})`)

    if (imageData.length !== expectedVoxelCount) {
      console.warn(`⚠️ 体素数量不匹配！实际: ${imageData.length}, 期望: ${expectedVoxelCount}`)
    }

    // 分析标签值
    const labels = this.analyzeLabelValues(imageData)

    return {
      data: imageData,
      dimensions: [header.dims[1], header.dims[2], header.dims[3]],
      labels
    }
  }

  /**
   * 根据NIFTI数据类型码创建正确的TypedArray
   */
  private static createTypedArrayFromBuffer(buffer: ArrayBuffer, datatypeCode: number): Uint8Array | Uint16Array | Int16Array | Float32Array {
    console.log(`🔧 创建TypedArray，数据类型码: ${datatypeCode}`)

    switch(datatypeCode) {
      case 2: // DT_UNSIGNED_CHAR
        console.log(`📊 使用 Uint8Array`)
        return new Uint8Array(buffer)
      case 4: // DT_SIGNED_SHORT
        console.log(`📊 使用 Int16Array`)
        return new Int16Array(buffer)
      case 8: // DT_SIGNED_INT
        console.log(`📊 使用 Int32Array，但返回为 Uint16Array 兼容`)
        return new Uint16Array(buffer) // 为了兼容性
      case 16: // DT_FLOAT
        console.log(`📊 使用 Float32Array`)
        return new Float32Array(buffer)
      case 256: // DT_INT8
        console.log(`📊 使用 Int8Array，但返回为 Uint8Array`)
        return new Uint8Array(buffer)
      case 512: // DT_UINT16
        console.log(`📊 使用 Uint16Array`)
        return new Uint16Array(buffer)
      case 768: // DT_UINT32
        console.log(`📊 使用 Uint32Array，但返回为 Uint16Array 兼容`)
        return new Uint16Array(buffer) // 为了兼容性
      default:
        console.warn(`⚠️ 未知数据类型码: ${datatypeCode}，默认使用 Uint8Array`)
        return new Uint8Array(buffer)
    }
  }

  /**
   * 简化的NIFTI头部解析
   */
  private static parseNiftiHeader(buffer: ArrayBuffer) {
    try {
      const view = new DataView(buffer)

      // 检查NIFTI魔数
      const magic = new Uint8Array(buffer.slice(344, 348))
      const magicStr = String.fromCharCode(...magic)

      console.log(`📋 NIFTI魔数: "${magicStr}"`)

      // 读取头部大小
      const headerSize = view.getInt32(0, true)
      console.log(`📋 头部大小: ${headerSize}`)

      // 读取基本维度信息 (偏移40-56)
      const dims = []
      for (let i = 0; i < 8; i++) {
        dims.push(view.getInt16(40 + i * 2, true)) // little endian
      }

      console.log(`📋 维度信息: [${dims.join(', ')}]`)

      // 读取数据类型 (偏移70-72)
      const datatypeCode = view.getInt16(70, true)
      console.log(`📋 数据类型码: ${datatypeCode}`)

      // 读取体素偏移量
      let voxOffset = view.getFloat32(108, true)
      if (voxOffset < 352) {
        voxOffset = 352 // 默认NIFTI偏移
      }

      console.log(`📋 体素数据偏移: ${voxOffset}`)

      // 验证维度信息
      if (dims[0] < 3 || dims[1] <= 0 || dims[2] <= 0 || dims[3] <= 0) {
        console.warn('⚠️ 维度信息异常，可能是字节序问题')
      }

      return {
        dims,
        datatypeCode,
        voxOffset: Math.floor(voxOffset),
        headerSize
      }
    } catch (error) {
      console.error('❌ NIFTI头部解析失败:', error)
      throw new Error(`NIFTI头部解析失败: ${error.message}`)
    }
  }

  /**
   * 分析标签值并生成标签映射
   */
  private static analyzeLabelValues(data: Uint8Array | Uint16Array | Int16Array | Float32Array): { [key: number]: string } {
    console.log(`🔍 开始分析标签值，数据长度: ${data.length}`)

    const uniqueValues = new Set<number>()
    const valueCounts: { [key: number]: number } = {}

    // 统计每个标签值的出现次数
    for (let i = 0; i < data.length; i++) {
      const value = data[i]
      uniqueValues.add(value)
      valueCounts[value] = (valueCounts[value] || 0) + 1
    }

    console.log(`🔍 检测到 ${uniqueValues.size} 个唯一值:`, Array.from(uniqueValues).sort())

    // 显示前100个数据值用于调试
    const sampleValues = Array.from(data.slice(0, 100))
    console.log(`🔍 前100个数据值:`, sampleValues)

    // 显示数据的统计信息
    const maxValue = Math.max(...Array.from(uniqueValues))
    const minValue = Math.min(...Array.from(uniqueValues))
    console.log(`🔍 数据范围: ${minValue} - ${maxValue}`)

    const labels: { [key: number]: string } = { 0: 'background' }

    // 为非零标签值生成有意义的名称
    const nonZeroValues = Array.from(uniqueValues).filter(v => v > 0).sort()
    console.log(`🔍 非零标签值:`, nonZeroValues)

    nonZeroValues.forEach((value, index) => {
      const count = valueCounts[value]
      // 根据标签值生成更有意义的名称
      if (value === 1) {
        labels[value] = 'structure_1'
      } else if (value === 2) {
        labels[value] = 'structure_2'
      } else if (value === 3) {
        labels[value] = 'structure_3'
      } else {
        labels[value] = `structure_${value}`
      }
    })

    console.log('检测到的标签值和体素数量:')
    Object.entries(valueCounts).forEach(([value, count]) => {
      console.log(`  标签 ${value}: ${count.toLocaleString()} 体素 ${parseInt(value) > 0 ? `(${labels[parseInt(value)]})` : '(background)'}`)
    })

    return labels
  }

  /**
   * 解压gzip数据（服务器端专用）
   */
  private static async decompressGzip(buffer: ArrayBuffer): Promise<ArrayBuffer> {
    try {
      console.log('🔧 使用Node.js zlib进行gzip解压...')
      const zlib = await import('zlib')
      const { promisify } = await import('util')
      const gunzip = promisify(zlib.gunzip)

      const compressed = Buffer.from(buffer)
      const decompressed = await gunzip(compressed)
      console.log(`✅ Node.js zlib解压成功: ${compressed.length} → ${decompressed.length} bytes`)
      return decompressed.buffer
    } catch (error) {
      console.error('❌ Node.js zlib解压失败:', error)
      throw new Error(`gzip解压失败: ${error.message}`)
    }
  }

  /**
   * 跳过gzip头部（备用方案，不推荐使用）
   */
  private static skipGzipHeader(buffer: ArrayBuffer): ArrayBuffer {
    console.warn('⚠️ 使用不安全的gzip头部跳过方法，这可能导致数据损坏')
    const view = new Uint8Array(buffer)

    // 检查gzip魔数
    if (view[0] === 0x1f && view[1] === 0x8b) {
      // 这不是真正的解压，只是跳过头部，数据仍然是压缩的
      console.warn('⚠️ 检测到gzip文件但无法正确解压，数据可能损坏')
      return buffer.slice(10) // 简单跳过基本头部
    }

    // 如果不是gzip格式，直接返回原数据
    return buffer
  }

  /**
   * 模拟分割过程（用于演示）
   */
  private static async simulateSegmentationProcess(): Promise<void> {
    const steps = [
      '预处理图像数据...',
      '加载分割模型...',
      '执行模型推理...',
      '后处理分割结果...',
      '质量检查...'
    ]
    
    for (const step of steps) {
      console.log(`🔄 ${step}`)
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  /**
   * 创建模拟的分割掩码（用于演示）
   */
  private static createMockSegmentationMask(
    calibration: DicomCalibration,
    modelName: string
  ): SegmentationMask {
    const config = this.modelConfigs[modelName]
    const [width, height, depth] = config.inputSize
    
    // 创建模拟的分割数据
    const mockData = new Uint8Array(width * height * depth)
    
    // 添加一些模拟的分割区域
    for (let z = 20; z < 40; z++) {
      for (let y = 200; y < 300; y++) {
        for (let x = 200; x < 300; x++) {
          const index = z * width * height + y * width + x
          mockData[index] = 1 // 肿瘤核心
        }
      }
    }
    
    return {
      id: `seg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      data: mockData,
      dimensions: [width, height, depth],
      labels: config.outputClasses,
      calibration,
      createdAt: new Date().toISOString(),
      modelVersion: config.modelVersion,
      confidence: 0.95
    }
  }

  /**
   * 获取可用的分割模型列表
   */
  static getAvailableModels(): SegmentationModelConfig[] {
    return Object.values(this.modelConfigs)
  }

  /**
   * 获取特定模型的配置
   */
  static getModelConfig(modelName: string): SegmentationModelConfig | null {
    return this.modelConfigs[modelName] || null
  }
}
