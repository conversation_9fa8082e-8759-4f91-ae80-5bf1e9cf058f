import { SegmentationMask, MeasurementResult, DicomCalibration } from './types'

/**
 * 测量计算服务类 - 基于分割掩码计算物理尺寸
 */
export class MeasurementService {
  
  /**
   * 计算指定标签的所有测量指标
   */
  static async calculateMeasurements(
    segmentation: SegmentationMask,
    labelId: number
  ): Promise<MeasurementResult> {
    console.log(`📏 开始计算标签 ${labelId} 的测量指标...`)
    
    const labelName = segmentation.labels[labelId] || `label_${labelId}`
    const { pixelSpacing, sliceThickness } = segmentation.calibration
    const [width, height, depth] = segmentation.dimensions
    
    // 提取指定标签的掩码
    const labelMask = this.extractLabelMask(segmentation.data, segmentation.dimensions, labelId)
    
    if (labelMask.voxelCount === 0) {
      throw new Error(`标签 ${labelId} 没有找到任何体素`)
    }
    
    console.log(`找到 ${labelMask.voxelCount} 个体素`)
    
    // 1. 计算体积
    const volumeResult = this.calculateVolume(labelMask, pixelSpacing, sliceThickness)
    
    // 2. 计算表面积（使用Marching Cubes算法）
    const surfaceAreaResult = await this.calculateSurfaceArea(
      labelMask,
      segmentation.dimensions,
      pixelSpacing,
      sliceThickness
    )
    
    // 3. 计算边界框
    const boundingBoxResult = this.calculateBoundingBox(labelMask, pixelSpacing, sliceThickness)
    
    // 4. 计算质心
    const centroidResult = this.calculateCentroid(labelMask, pixelSpacing, sliceThickness)
    
    // 5. 评估网格质量
    const qualityResult = this.evaluateMeshQuality(surfaceAreaResult.meshData)
    
    return {
      id: `measure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      segmentationId: segmentation.id,
      labelId,
      labelName,
      measurements: {
        volume: volumeResult,
        surfaceArea: {
          physicalArea: surfaceAreaResult.area,
          physicalAreaCM2: surfaceAreaResult.area / 100 // mm² to cm²
        },
        boundingBox: boundingBoxResult,
        centroid: centroidResult
      },
      quality: qualityResult,
      createdAt: new Date().toISOString()
    }
  }
  
  /**
   * 提取指定标签的掩码数据
   */
  private static extractLabelMask(
    data: Uint8Array | Uint16Array | Int16Array | Float32Array,
    dimensions: [number, number, number],
    labelId: number
  ) {
    const [width, height, depth] = dimensions
    const voxels: Array<[number, number, number]> = []
    
    for (let z = 0; z < depth; z++) {
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = z * width * height + y * width + x
          if (data[index] === labelId) {
            voxels.push([x, y, z])
          }
        }
      }
    }
    
    return {
      voxels,
      voxelCount: voxels.length
    }
  }
  
  /**
   * 计算体积
   */
  private static calculateVolume(
    labelMask: { voxels: Array<[number, number, number]>; voxelCount: number },
    pixelSpacing: [number, number],
    sliceThickness: number
  ) {
    const voxelVolume = pixelSpacing[0] * pixelSpacing[1] * sliceThickness // mm³
    const physicalVolume = labelMask.voxelCount * voxelVolume
    const physicalVolumeML = physicalVolume / 1000 // mm³ to mL
    
    console.log(`📊 体积计算:`)
    console.log(`  体素数量: ${labelMask.voxelCount}`)
    console.log(`  单体素体积: ${voxelVolume.toFixed(3)} mm³`)
    console.log(`  总体积: ${physicalVolume.toFixed(2)} mm³ (${physicalVolumeML.toFixed(2)} mL)`)
    
    return {
      voxelCount: labelMask.voxelCount,
      physicalVolume,
      physicalVolumeML
    }
  }
  
  /**
   * 使用Marching Cubes算法计算表面积
   * 注意：这是简化实现，实际应用中需要使用专业的网格生成库
   */
  private static async calculateSurfaceArea(
    labelMask: { voxels: Array<[number, number, number]>; voxelCount: number },
    dimensions: [number, number, number],
    pixelSpacing: [number, number],
    sliceThickness: number
  ): Promise<{ area: number; meshData: any }> {
    console.log(`🔺 使用Marching Cubes算法计算表面积...`)
    
    // TODO: 实际实现中需要使用专业的Marching Cubes库
    // 例如：three.js的MarchingCubes或vtk.js
    
    // 1. 创建体素网格
    const voxelGrid = this.createVoxelGrid(labelMask.voxels, dimensions)
    
    // 2. 应用Marching Cubes算法生成三角网格
    const mesh = this.simplifiedMarchingCubes(voxelGrid, dimensions, pixelSpacing, sliceThickness)
    
    // 3. 计算三角形面积并累加
    const totalArea = this.calculateTriangleMeshArea(mesh.triangles)
    
    console.log(`  生成三角形数量: ${mesh.triangles.length}`)
    console.log(`  表面积: ${totalArea.toFixed(2)} mm²`)
    
    return {
      area: totalArea,
      meshData: mesh
    }
  }
  
  /**
   * 创建体素网格
   */
  private static createVoxelGrid(
    voxels: Array<[number, number, number]>,
    dimensions: [number, number, number]
  ): boolean[][][] {
    const [width, height, depth] = dimensions
    const grid: boolean[][][] = Array(depth).fill(null).map(() =>
      Array(height).fill(null).map(() => Array(width).fill(false))
    )
    
    voxels.forEach(([x, y, z]) => {
      if (x >= 0 && x < width && y >= 0 && y < height && z >= 0 && z < depth) {
        grid[z][y][x] = true
      }
    })
    
    return grid
  }
  
  /**
   * 简化的Marching Cubes实现
   * 注意：这是教学用的简化版本，实际应用需要完整实现
   */
  private static simplifiedMarchingCubes(
    grid: boolean[][][],
    dimensions: [number, number, number],
    pixelSpacing: [number, number],
    sliceThickness: number
  ) {
    const triangles: Array<{
      vertices: [[number, number, number], [number, number, number], [number, number, number]]
      area: number
    }> = []
    
    const [width, height, depth] = dimensions
    
    // 简化版：只检测表面体素并生成近似三角形
    for (let z = 1; z < depth - 1; z++) {
      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          if (grid[z][y][x]) {
            // 检查是否为表面体素（至少有一个邻居为空）
            const neighbors = [
              grid[z][y][x-1], grid[z][y][x+1],
              grid[z][y-1][x], grid[z][y+1][x],
              grid[z-1][y][x], grid[z+1][y][x]
            ]
            
            if (neighbors.some(n => !n)) {
              // 为表面体素生成近似三角形
              const physicalX = x * pixelSpacing[0]
              const physicalY = y * pixelSpacing[1]
              const physicalZ = z * sliceThickness
              
              // 生成立方体的一些面作为三角形（简化）
              const cubeTriangles = this.generateCubeTriangles(
                physicalX, physicalY, physicalZ,
                pixelSpacing[0], pixelSpacing[1], sliceThickness
              )
              
              triangles.push(...cubeTriangles)
            }
          }
        }
      }
    }
    
    return {
      triangles,
      vertices: triangles.length * 3
    }
  }
  
  /**
   * 为体素生成立方体三角形（简化）
   */
  private static generateCubeTriangles(
    x: number, y: number, z: number,
    dx: number, dy: number, dz: number
  ) {
    // 简化：只生成立方体的两个三角形面
    const v1: [number, number, number] = [x, y, z]
    const v2: [number, number, number] = [x + dx, y, z]
    const v3: [number, number, number] = [x, y + dy, z]
    const v4: [number, number, number] = [x + dx, y + dy, z]
    
    const triangle1 = { vertices: [v1, v2, v3] as [[number, number, number], [number, number, number], [number, number, number]], area: 0 }
    const triangle2 = { vertices: [v2, v3, v4] as [[number, number, number], [number, number, number], [number, number, number]], area: 0 }
    
    triangle1.area = this.calculateTriangleArea(triangle1.vertices)
    triangle2.area = this.calculateTriangleArea(triangle2.vertices)
    
    return [triangle1, triangle2]
  }
  
  /**
   * 计算三角形面积
   */
  private static calculateTriangleArea(vertices: [[number, number, number], [number, number, number], [number, number, number]]): number {
    const [v1, v2, v3] = vertices
    
    // 使用向量叉积计算面积
    const edge1 = [v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2]]
    const edge2 = [v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2]]
    
    const cross = [
      edge1[1] * edge2[2] - edge1[2] * edge2[1],
      edge1[2] * edge2[0] - edge1[0] * edge2[2],
      edge1[0] * edge2[1] - edge1[1] * edge2[0]
    ]
    
    const magnitude = Math.sqrt(cross[0] * cross[0] + cross[1] * cross[1] + cross[2] * cross[2])
    return magnitude / 2
  }
  
  /**
   * 计算三角网格的总表面积
   */
  private static calculateTriangleMeshArea(triangles: Array<{ area: number }>): number {
    return triangles.reduce((total, triangle) => total + triangle.area, 0)
  }
  
  /**
   * 计算边界框
   */
  private static calculateBoundingBox(
    labelMask: { voxels: Array<[number, number, number]> },
    pixelSpacing: [number, number],
    sliceThickness: number
  ) {
    if (labelMask.voxels.length === 0) {
      throw new Error('没有体素数据')
    }
    
    let minX = Infinity, maxX = -Infinity
    let minY = Infinity, maxY = -Infinity
    let minZ = Infinity, maxZ = -Infinity
    
    labelMask.voxels.forEach(([x, y, z]) => {
      minX = Math.min(minX, x)
      maxX = Math.max(maxX, x)
      minY = Math.min(minY, y)
      maxY = Math.max(maxY, y)
      minZ = Math.min(minZ, z)
      maxZ = Math.max(maxZ, z)
    })
    
    const physicalSize: [number, number, number] = [
      (maxX - minX + 1) * pixelSpacing[0],
      (maxY - minY + 1) * pixelSpacing[1],
      (maxZ - minZ + 1) * sliceThickness
    ]
    
    console.log(`📦 边界框:`)
    console.log(`  体素坐标: (${minX},${minY},${minZ}) - (${maxX},${maxY},${maxZ})`)
    console.log(`  物理尺寸: ${physicalSize[0].toFixed(2)} × ${physicalSize[1].toFixed(2)} × ${physicalSize[2].toFixed(2)} mm`)
    
    return {
      min: [minX, minY, minZ] as [number, number, number],
      max: [maxX, maxY, maxZ] as [number, number, number],
      physicalSize
    }
  }
  
  /**
   * 计算质心
   */
  private static calculateCentroid(
    labelMask: { voxels: Array<[number, number, number]> },
    pixelSpacing: [number, number],
    sliceThickness: number
  ) {
    if (labelMask.voxels.length === 0) {
      throw new Error('没有体素数据')
    }
    
    let sumX = 0, sumY = 0, sumZ = 0
    
    labelMask.voxels.forEach(([x, y, z]) => {
      sumX += x
      sumY += y
      sumZ += z
    })
    
    const voxelCoords: [number, number, number] = [
      sumX / labelMask.voxels.length,
      sumY / labelMask.voxels.length,
      sumZ / labelMask.voxels.length
    ]
    
    const physicalCoords: [number, number, number] = [
      voxelCoords[0] * pixelSpacing[0],
      voxelCoords[1] * pixelSpacing[1],
      voxelCoords[2] * sliceThickness
    ]
    
    console.log(`🎯 质心:`)
    console.log(`  体素坐标: (${voxelCoords[0].toFixed(2)}, ${voxelCoords[1].toFixed(2)}, ${voxelCoords[2].toFixed(2)})`)
    console.log(`  物理坐标: (${physicalCoords[0].toFixed(2)}, ${physicalCoords[1].toFixed(2)}, ${physicalCoords[2].toFixed(2)}) mm`)
    
    return {
      voxelCoords,
      physicalCoords
    }
  }
  
  /**
   * 评估网格质量
   */
  private static evaluateMeshQuality(meshData: any) {
    const vertices = meshData.vertices || 0
    const faces = meshData.triangles?.length || 0
    
    // 简化的质量评估
    const isWatertight = faces > 0 // 简化判断
    const smoothness = Math.min(1.0, vertices / (faces * 3)) // 简化平滑度计算
    
    console.log(`🔍 网格质量:`)
    console.log(`  顶点数: ${vertices}`)
    console.log(`  面数: ${faces}`)
    console.log(`  水密性: ${isWatertight ? '是' : '否'}`)
    console.log(`  平滑度: ${smoothness.toFixed(3)}`)
    
    return {
      meshVertices: vertices,
      meshFaces: faces,
      isWatertight,
      smoothness
    }
  }
  
  /**
   * 批量计算所有标签的测量指标
   */
  static async calculateAllMeasurements(segmentation: SegmentationMask): Promise<MeasurementResult[]> {
    const results: MeasurementResult[] = []
    
    // 获取所有非背景标签
    const labelIds = Object.keys(segmentation.labels)
      .map(Number)
      .filter(id => id > 0) // 排除背景标签0
    
    console.log(`📊 开始计算 ${labelIds.length} 个标签的测量指标...`)
    
    for (const labelId of labelIds) {
      try {
        const result = await this.calculateMeasurements(segmentation, labelId)
        results.push(result)
        console.log(`✅ 标签 ${labelId} 计算完成`)
      } catch (error) {
        console.error(`❌ 标签 ${labelId} 计算失败:`, error)
      }
    }
    
    return results
  }
}
