"use client"

import React from "react"
import { Loader2, Upload, FileText, Brain, CheckCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

// 骨架屏组件
export function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  )
}

// 患者列表骨架屏
export function PatientListSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-3 border rounded-md">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-3 w-[150px]" />
          </div>
        </div>
      ))}
    </div>
  )
}

// 报告列表骨架屏
export function ReportListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-[150px]" />
              <Skeleton className="h-4 w-[80px]" />
            </div>
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-[80%]" />
              <Skeleton className="h-3 w-[60%]" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// 文件上传进度组件
interface FileUploadProgressProps {
  fileName: string
  progress: number
  status: "uploading" | "processing" | "completed" | "error"
  error?: string
}

export function FileUploadProgress({ fileName, progress, status, error }: FileUploadProgressProps) {
  const getStatusIcon = () => {
    switch (status) {
      case "uploading":
        return <Upload className="h-5 w-5 text-blue-500" />
      case "processing":
        return <Brain className="h-5 w-5 text-orange-500" />
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "error":
        return <FileText className="h-5 w-5 text-red-500" />
      default:
        return <Loader2 className="h-5 w-5 animate-spin" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case "uploading":
        return "上传中..."
      case "processing":
        return "分析中..."
      case "completed":
        return "完成"
      case "error":
        return "错误"
      default:
        return "处理中..."
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case "uploading":
        return "text-blue-600"
      case "processing":
        return "text-orange-600"
      case "completed":
        return "text-green-600"
      case "error":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{fileName}</p>
              <p className={cn("text-sm", getStatusColor())}>{getStatusText()}</p>
            </div>
            <div className="text-sm text-gray-500">
              {status !== "error" && `${Math.round(progress)}%`}
            </div>
          </div>

          {status !== "error" && (
            <Progress value={progress} className="w-full" />
          )}

          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// CT分析进度组件
interface AnalysisProgressProps {
  currentStep: number
  totalSteps: number
  stepName: string
  isComplete: boolean
}

export function AnalysisProgress({ currentStep, totalSteps, stepName, isComplete }: AnalysisProgressProps) {
  const progress = (currentStep / totalSteps) * 100

  const steps = [
    "文件验证",
    "数据预处理",
    "自动分割",
    "测量计算",
    "报告生成"
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {isComplete ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
          )}
          <span>CT影像分析</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{isComplete ? "分析完成" : stepName}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        <div className="space-y-2">
          {steps.map((step, index) => (
            <div key={step} className="flex items-center space-x-2">
              <div
                className={cn(
                  "w-2 h-2 rounded-full",
                  index < currentStep
                    ? "bg-green-500"
                    : index === currentStep
                    ? "bg-blue-500"
                    : "bg-gray-300"
                )}
              />
              <span
                className={cn(
                  "text-sm",
                  index < currentStep
                    ? "text-green-600"
                    : index === currentStep
                    ? "text-blue-600"
                    : "text-gray-500"
                )}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// 通用加载指示器
interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  text?: string
  className?: string
}

export function LoadingSpinner({ size = "md", text, className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  }

  return (
    <div className={cn("flex items-center justify-center space-x-2", className)}>
      <Loader2 className={cn("animate-spin", sizeClasses[size])} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  )
}

// 页面级加载组件
export function PageLoading({ message = "加载中..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}
