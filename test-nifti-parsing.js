// 简单的NIFTI解析测试脚本
const fs = require('fs');
const path = require('path');

// 模拟SegmentationService的关键方法
class TestSegmentationService {
  static async decompressGzip(buffer) {
    try {
      console.log('🔧 使用Node.js zlib进行gzip解压...')
      const zlib = require('zlib')
      const { promisify } = require('util')
      const gunzip = promisify(zlib.gunzip)

      const compressed = Buffer.from(buffer)
      const decompressed = await gunzip(compressed)
      console.log(`✅ Node.js zlib解压成功: ${compressed.length} → ${decompressed.length} bytes`)
      return decompressed.buffer
    } catch (error) {
      console.error('❌ Node.js zlib解压失败:', error)
      throw new Error(`gzip解压失败: ${error.message}`)
    }
  }

  static parseNiftiHeader(buffer) {
    try {
      const view = new DataView(buffer)

      // 检查NIFTI魔数
      const magic = new Uint8Array(buffer.slice(344, 348))
      const magicStr = String.fromCharCode(...magic)

      console.log(`📋 NIFTI魔数: "${magicStr}"`)

      // 读取头部大小
      const headerSize = view.getInt32(0, true)
      console.log(`📋 头部大小: ${headerSize}`)

      // 读取基本维度信息 (偏移40-56)
      const dims = []
      for (let i = 0; i < 8; i++) {
        dims.push(view.getInt16(40 + i * 2, true)) // little endian
      }

      console.log(`📋 维度信息: [${dims.join(', ')}]`)

      // 读取数据类型 (偏移70-72)
      const datatypeCode = view.getInt16(70, true)
      console.log(`📋 数据类型码: ${datatypeCode}`)

      // 读取体素偏移量
      let voxOffset = view.getFloat32(108, true)
      if (voxOffset < 352) {
        voxOffset = 352 // 默认NIFTI偏移
      }

      console.log(`📋 体素数据偏移: ${voxOffset}`)

      return {
        dims,
        datatypeCode,
        voxOffset: Math.floor(voxOffset),
        headerSize
      }
    } catch (error) {
      console.error('❌ NIFTI头部解析失败:', error)
      throw new Error(`NIFTI头部解析失败: ${error.message}`)
    }
  }

  static createTypedArrayFromBuffer(buffer, datatypeCode) {
    console.log(`🔧 创建TypedArray，数据类型码: ${datatypeCode}`)
    
    switch(datatypeCode) {
      case 2: // DT_UNSIGNED_CHAR
        console.log(`📊 使用 Uint8Array`)
        return new Uint8Array(buffer)
      case 4: // DT_SIGNED_SHORT  
        console.log(`📊 使用 Int16Array`)
        return new Int16Array(buffer)
      case 16: // DT_FLOAT
        console.log(`📊 使用 Float32Array`)
        return new Float32Array(buffer)
      case 512: // DT_UINT16
        console.log(`📊 使用 Uint16Array`)
        return new Uint16Array(buffer)
      default: 
        console.warn(`⚠️ 未知数据类型码: ${datatypeCode}，默认使用 Uint8Array`)
        return new Uint8Array(buffer)
    }
  }

  static analyzeLabelValues(data) {
    console.log(`🔍 开始分析标签值，数据长度: ${data.length}`)
    
    const uniqueValues = new Set()
    const valueCounts = {}

    // 统计每个标签值的出现次数
    for (let i = 0; i < data.length; i++) {
      const value = data[i]
      uniqueValues.add(value)
      valueCounts[value] = (valueCounts[value] || 0) + 1
    }

    console.log(`🔍 检测到 ${uniqueValues.size} 个唯一值:`, Array.from(uniqueValues).sort())
    
    // 显示前100个数据值用于调试
    const sampleValues = Array.from(data.slice(0, 100))
    console.log(`🔍 前100个数据值:`, sampleValues)
    
    // 显示数据的统计信息
    const maxValue = Math.max(...Array.from(uniqueValues))
    const minValue = Math.min(...Array.from(uniqueValues))
    console.log(`🔍 数据范围: ${minValue} - ${maxValue}`)

    const labels = { 0: 'background' }

    // 为非零标签值生成有意义的名称
    const nonZeroValues = Array.from(uniqueValues).filter(v => v > 0).sort()
    console.log(`🔍 非零标签值:`, nonZeroValues)

    nonZeroValues.forEach((value, index) => {
      labels[value] = `structure_${value}`
    })

    console.log('检测到的标签值和体素数量:')
    Object.entries(valueCounts).forEach(([value, count]) => {
      console.log(`  标签 ${value}: ${count.toLocaleString()} 体素 ${parseInt(value) > 0 ? `(${labels[parseInt(value)]})` : '(background)'}`)
    })

    return labels
  }

  static async parseNiftiLabel(arrayBuffer) {
    console.log(`🔍 解析NIFTI标签文件，原始大小: ${arrayBuffer.byteLength} bytes`)
    
    // 检查是否为gzip压缩
    const firstBytes = new Uint8Array(arrayBuffer.slice(0, 2))
    let decompressedBuffer = arrayBuffer

    if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
      console.log('🗜️ 检测到gzip压缩，正在解压...')
      try {
        decompressedBuffer = await this.decompressGzip(arrayBuffer)
        console.log(`✅ gzip解压成功，解压后大小: ${decompressedBuffer.byteLength} bytes`)
      } catch (error) {
        console.error('❌ gzip解压失败:', error)
        throw error
      }
    } else {
      console.log('📄 未检测到gzip压缩，直接解析')
    }
    
    // 简化的NIFTI头部解析
    const header = this.parseNiftiHeader(decompressedBuffer)
    console.log(`📋 NIFTI头部解析完成:`, header)
    
    // 提取图像数据
    const rawImageData = decompressedBuffer.slice(header.voxOffset)
    console.log(`🖼️ 提取的原始图像数据长度: ${rawImageData.byteLength} bytes`)
    
    // 根据数据类型创建正确的TypedArray
    const imageData = this.createTypedArrayFromBuffer(rawImageData, header.datatypeCode)
    console.log(`🖼️ 创建的TypedArray长度: ${imageData.length}，类型: ${imageData.constructor.name}`)
    
    // 计算期望的数据大小
    const expectedVoxelCount = header.dims[1] * header.dims[2] * header.dims[3]
    console.log(`📐 期望的体素数量: ${expectedVoxelCount} (${header.dims[1]} × ${header.dims[2]} × ${header.dims[3]})`)
    
    if (imageData.length !== expectedVoxelCount) {
      console.warn(`⚠️ 体素数量不匹配！实际: ${imageData.length}, 期望: ${expectedVoxelCount}`)
    }
    
    // 分析标签值
    const labels = this.analyzeLabelValues(imageData)
    
    return {
      data: imageData,
      dimensions: [header.dims[1], header.dims[2], header.dims[3]],
      labels
    }
  }
}

// 测试函数
async function testNiftiFile(filePath) {
  try {
    console.log(`\n🧪 测试文件: ${filePath}`)
    
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 文件不存在: ${filePath}`)
      return
    }
    
    const buffer = fs.readFileSync(filePath).buffer
    console.log(`📁 文件读取成功，大小: ${buffer.byteLength} bytes`)
    
    const result = await TestSegmentationService.parseNiftiLabel(buffer)
    
    console.log(`\n✅ 解析成功！`)
    console.log(`📐 维度: ${result.dimensions.join(' × ')}`)
    console.log(`🏷️ 标签数量: ${Object.keys(result.labels).length}`)
    console.log(`📊 数据类型: ${result.data.constructor.name}`)
    console.log(`📊 数据长度: ${result.data.length}`)
    
  } catch (error) {
    console.error(`❌ 测试失败:`, error.message)
  }
}

// 如果作为脚本运行
if (require.main === module) {
  const filePath = process.argv[2]
  if (!filePath) {
    console.log('用法: node test-nifti-parsing.js <nifti文件路径>')
    console.log('例如: node test-nifti-parsing.js uploads/labels/example.nii.gz')
  } else {
    testNiftiFile(filePath)
  }
}

module.exports = { TestSegmentationService, testNiftiFile }
