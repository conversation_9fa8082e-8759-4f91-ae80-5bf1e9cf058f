// 测试标签模式的完整流程
const fs = require('fs');
const FormData = require('form-data');
const { default: fetch } = require('node-fetch');

async function testLabelMode() {
  try {
    console.log('🧪 开始测试标签模式...');
    
    // 读取标签文件
    const labelFilePath = 'data/uploads/labels/1749649991847-301_trans.nii.gz';
    if (!fs.existsSync(labelFilePath)) {
      console.error('❌ 标签文件不存在:', labelFilePath);
      return;
    }
    
    const labelBuffer = fs.readFileSync(labelFilePath);
    console.log(`📁 读取标签文件: ${labelFilePath} (${labelBuffer.length} bytes)`);
    
    // 创建FormData
    const formData = new FormData();
    formData.append('labelFile', labelBuffer, {
      filename: '301_trans.nii.gz',
      contentType: 'application/gzip'
    });
    formData.append('mode', 'label');
    formData.append('modelName', 'tumor-segmentation-v1');
    
    console.log('📤 发送请求到分割API...');
    
    // 发送请求
    const response = await fetch('http://localhost:3000/api/segmentation', {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      console.error('❌ API请求失败:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('错误详情:', errorText);
      return;
    }
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 分割和测量成功！');
      console.log('📊 结果摘要:');
      console.log(`  - 分割ID: ${result.data.segmentation.id}`);
      console.log(`  - 维度: ${result.data.segmentation.dimensions.join(' × ')}`);
      console.log(`  - 校准信息:`, result.data.segmentation.calibration);
      console.log(`  - 检测到的标签数量: ${Object.keys(result.data.segmentation.labels).length}`);
      console.log(`  - 测量结果数量: ${result.data.measurements.length}`);
      
      console.log('\n🏷️ 检测到的标签:');
      Object.entries(result.data.segmentation.labels).forEach(([id, name]) => {
        console.log(`  标签 ${id}: ${name}`);
      });
      
      console.log('\n📏 测量结果:');
      result.data.measurements.forEach((measurement, index) => {
        console.log(`  ${index + 1}. ${measurement.labelName} (标签 ${measurement.labelId}):`);
        console.log(`     体积: ${measurement.measurements.volume.physicalVolumeML.toFixed(2)} mL`);
        console.log(`     表面积: ${(measurement.measurements.surfaceArea.physicalAreaCM2).toFixed(2)} cm²`);
        console.log(`     体素数量: ${measurement.measurements.volume.voxelCount.toLocaleString()}`);
      });
      
    } else {
      console.error('❌ 分割失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testLabelMode();
}

module.exports = { testLabelMode };
